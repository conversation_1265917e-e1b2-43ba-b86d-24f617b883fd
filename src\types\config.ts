export interface AppConfig {
  name: string;
  version: string;
  environment: string;
  port: number;
  apiPrefix: string;
}

export interface ZaloConfig {
  cookie?: string;
  userAgent?: string;
  webhookUrl?: string;
  webhookSecret?: string;
}

export interface LoggingConfig {
  level: string;
  format: string;
}

export interface Config {
  app: AppConfig;
  zalo: ZaloConfig;
  logging: LoggingConfig;
}
