import { Config } from '../types/config';

// Declare Node.js globals
declare const process: any;
declare const parseInt: any;

export const config: Config = {
  app: {
    name: 'Zalo Chatbot',
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    port: parseInt(process.env.PORT || '3000', 10),
  },
  zalo: {
    cookie: process.env.ZALO_COOKIE || '',
    userAgent: process.env.ZALO_USER_AGENT || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    webhookUrl: process.env.WEBHOOK_URL || '',
    webhookSecret: process.env.WEBHOOK_SECRET || '',
  },
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: process.env.LOG_FORMAT || 'combined',
  },
};

// Validate required environment variables
export function validateConfig(): void {
  // Với zca-js, chúng ta sẽ validate trong quá trình đăng nhập
  // Không cần validate environment variables ở đây
  // console.log('Config validation passed - using zca-js authentication');
}
