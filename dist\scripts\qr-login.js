"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const logger_1 = require("../utils/logger");
const { Zalo } = require('zca-js');
async function loginWithQR() {
    try {
        logger_1.logger.info('🔐 Starting QR Code login process...');
        const zalo = new Zalo({
            selfListen: false,
        });
        logger_1.logger.info('📱 Please scan the QR code that will appear...');
        const api = await zalo.loginQR();
        const cookie = await api.getCookie();
        logger_1.logger.info('✅ Login successful!');
        logger_1.logger.info('🍪 Your cookie (save this to .env file):');
        console.log('\n' + '='.repeat(80));
        console.log('ZALO_COOKIE=' + cookie);
        console.log('='.repeat(80) + '\n');
        const accountInfo = await api.fetchAccountInfo();
        logger_1.logger.info(`👤 Logged in as: ${accountInfo.name} (${accountInfo.id})`);
        logger_1.logger.info('💡 Copy the cookie above and paste it into your .env file');
        logger_1.logger.info('🚀 Then you can start your bot with: yarn dev');
    }
    catch (error) {
        logger_1.logger.error('❌ QR Login failed:', error);
        process.exit(1);
    }
}
loginWithQR().catch(error => {
    logger_1.logger.error('💥 Unhandled error:', error);
    process.exit(1);
});
//# sourceMappingURL=qr-login.js.map