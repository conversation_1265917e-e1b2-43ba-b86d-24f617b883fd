import { ZaloBot } from './services/ZaloBot';
import { ExpressApp } from './app';
import { config } from './config/config';
import { logger } from './utils/logger';

// Declare Node.js globals
declare const process: any;

async function main(): Promise<void> {
  try {
    logger.info('🚀 Starting Zalo Chatbot System...');

    // Khởi tạo Express API Server
    logger.info('📡 Starting Express API Server...');
    const expressApp = new ExpressApp();
    expressApp.start();

    // Khởi tạo Zalo Bo<PERSON> (nếu cần)
    if (process.env.ENABLE_ZALO_BOT !== 'false') {
      logger.info('🤖 Starting Zalo Bot...');
      const bot = new ZaloBot(config.zalo);
      await bot.initialize();
      logger.info('✅ Zalo Bot started successfully!');
    } else {
      logger.info('⏭️ Zalo Bot disabled - running API server only');
    }

    logger.info('✅ Zalo Chatbot System started successfully!');
  } catch (error) {
    logger.error('❌ Failed to start Zalo Chatbot System:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGINT', () => {
  logger.info('🛑 Shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  logger.info('🛑 Shutting down gracefully...');
  process.exit(0);
});

// Start the application
main().catch(error => {
  logger.error('💥 Unhandled error:', error);
  process.exit(1);
});
