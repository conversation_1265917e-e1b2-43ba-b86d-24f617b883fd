# Zalo Chatbot API Guide

## Tổng quan

API Express được tích hợp để nhận và xử lý tin nhắn từ Zalo webhook một cách tối ưu và dễ quản lý.

## Cấu trúc dự án

```
src/
├── app.ts                    # Express application setup
├── index.ts                  # Main entry point
├── controllers/
│   └── zaloController.ts     # Xử lý webhook requests
├── middleware/
│   ├── errorHandler.ts       # Error handling middleware
│   └── zaloAuth.ts          # Zalo webhook authentication
├── routes/
│   └── zalo.ts              # API routes
├── services/
│   └── ZaloMessageService.ts # Business logic xử lý tin nhắn
├── types/
│   ├── config.ts            # Configuration types
│   └── zalo.ts              # Zalo webhook types
└── config/
    └── config.ts            # Application configuration
```

## Cài đặt và chạy

### 1. Cài đặt dependencies
```bash
yarn install
```

### 2. Cấu hình environment variables
Sao chép `.env.example` thành `.env` và cấu hình:

```bash
cp .env.example .env
```

### 3. Chạy ứng dụng

#### Chạy full system (API + Bot):
```bash
yarn dev
```

#### Chỉ chạy API server:
```bash
yarn dev:api
```

#### Production:
```bash
yarn build
yarn start        # Full system
yarn start:api    # Chỉ API server
```

## API Endpoints

### Base URL
```
http://localhost:3000/api/v1
```

### 1. Health Check
```
GET /health
GET /api/v1/health
```

Response:
```json
{
  "status": "OK",
  "service": "Zalo API",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "uptime": 123.456
}
```

### 2. Test Endpoint
```
GET /api/v1/test
```

Response:
```json
{
  "message": "Zalo API is working!",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "environment": "development"
}
```

### 3. Webhook Endpoint (POST)
```
POST /api/v1/webhook
```

Nhận tin nhắn từ Zalo và xử lý tự động.

Request Headers:
- `Content-Type: application/json`
- `X-Zalo-Signature: <signature>` (nếu có webhook secret)
- `X-Zalo-Timestamp: <timestamp>`

Response:
```json
{
  "status": "ok",
  "message": "Webhook processed successfully",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 4. Webhook Verification (GET)
```
GET /api/v1/webhook?hub.challenge=<challenge>&hub.verify_token=<token>
```

Dùng để verify webhook URL với Zalo.

## Cấu hình Zalo Webhook

### 1. Cấu hình trong Zalo Developer Console:
- Webhook URL: `https://your-domain.com/api/v1/webhook`
- Verify Token: Giá trị trong `ZALO_VERIFY_TOKEN`

### 2. Environment Variables cần thiết:
```env
WEBHOOK_URL=https://your-domain.com/api/v1/webhook
WEBHOOK_SECRET=your-webhook-secret-key
ZALO_VERIFY_TOKEN=your-verify-token
```

## Xử lý tin nhắn

### Các loại tin nhắn được hỗ trợ:
- ✅ Text messages
- ✅ Image messages  
- ✅ File messages
- ✅ Audio messages
- ✅ Video messages
- ✅ Location messages
- ✅ Sticker messages
- ✅ Follow/Unfollow events

### Auto-reply Logic:
API có sẵn logic auto-reply đơn giản:
- "hello", "hi", "chào" → Tin nhắn chào hỏi
- "help", "giúp" → Hướng dẫn sử dụng
- "bye", "tạm biệt" → Tin nhắn tạm biệt
- Tin nhắn khác → Response xác nhận đã nhận

### Tùy chỉnh xử lý tin nhắn:
Chỉnh sửa file `src/services/ZaloMessageService.ts` để:
- Thêm logic AI chatbot
- Kết nối database
- Tích hợp external APIs
- Custom auto-reply rules

## Security Features

### 1. Webhook Signature Verification
- Xác thực tin nhắn từ Zalo bằng HMAC-SHA256
- Kiểm tra timestamp để tránh replay attacks

### 2. CORS Protection
- Cấu hình CORS cho phép origins cụ thể
- Headers security với Helmet

### 3. Rate Limiting & Input Validation
- Validation payload structure
- Error handling middleware

## Logging & Monitoring

### Log Levels:
- `info`: Thông tin hoạt động bình thường
- `warn`: Cảnh báo
- `error`: Lỗi cần xử lý

### Log Format:
- Request/Response logging
- Error tracking với stack trace
- Webhook event logging

## Development Tips

### 1. Testing Webhook:
```bash
# Test với curl
curl -X POST http://localhost:3000/api/v1/webhook \
  -H "Content-Type: application/json" \
  -d '{"app_id":"test","user_id_by_app":"123","event_name":"user_send_text","message":{"message":{"text":"Hello"}},"timestamp":1234567890}'
```

### 2. Debug Mode:
Set `LOG_LEVEL=debug` trong `.env` để xem chi tiết logs.

### 3. Disable Bot:
Set `ENABLE_ZALO_BOT=false` để chỉ chạy API server.

## Deployment

### 1. Build Production:
```bash
yarn build
```

### 2. Environment Variables Production:
```env
NODE_ENV=production
PORT=3000
WEBHOOK_URL=https://your-domain.com/api/v1/webhook
ALLOWED_ORIGINS=https://your-domain.com
```

### 3. Process Manager (PM2):
```bash
pm2 start dist/index.js --name "zalo-chatbot"
```

## Troubleshooting

### Common Issues:

1. **Webhook verification failed**
   - Kiểm tra `WEBHOOK_SECRET` và `ZALO_VERIFY_TOKEN`
   - Đảm bảo timestamp không quá cũ (< 5 phút)

2. **CORS errors**
   - Cấu hình `ALLOWED_ORIGINS` đúng domain

3. **Port already in use**
   - Thay đổi `PORT` trong `.env`
   - Kill process đang sử dụng port: `lsof -ti:3000 | xargs kill`

### Debug Commands:
```bash
# Check logs
yarn dev | grep ERROR

# Test API health
curl http://localhost:3000/health

# Validate webhook
curl http://localhost:3000/api/v1/webhook?hub.challenge=test&hub.verify_token=your-token
```
