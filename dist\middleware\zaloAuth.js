"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateZaloPayload = exports.verifyZaloSignature = exports.rawBodyParser = void 0;
const crypto_1 = __importDefault(require("crypto"));
const config_1 = require("../config/config");
const logger_1 = require("../utils/logger");
const errorHandler_1 = require("./errorHandler");
const rawBodyParser = (req, res, next) => {
    let data = '';
    req.setEncoding('utf8');
    req.on('data', (chunk) => {
        data += chunk;
    });
    req.on('end', () => {
        req.rawBody = Buffer.from(data, 'utf8');
        req.body = data ? JSON.parse(data) : {};
        next();
    });
};
exports.rawBodyParser = rawBodyParser;
const verifyZaloSignature = (req, res, next) => {
    try {
        const signature = req.headers['x-zalo-signature'];
        const timestamp = req.headers['x-zalo-timestamp'];
        if (!config_1.config.zalo.webhookSecret) {
            logger_1.logger.warn('Webhook secret not configured - skipping signature verification');
            return next();
        }
        if (!signature || !timestamp) {
            throw new errorHandler_1.AppError('Missing Zalo signature or timestamp headers', 401);
        }
        const requestTime = parseInt(timestamp);
        const currentTime = Math.floor(Date.now() / 1000);
        const timeDiff = Math.abs(currentTime - requestTime);
        if (timeDiff > 300) {
            throw new errorHandler_1.AppError('Request timestamp too old', 401);
        }
        const rawBody = req.rawBody?.toString('utf8') || '';
        const signatureData = `${timestamp}${rawBody}`;
        const expectedSignature = crypto_1.default
            .createHmac('sha256', config_1.config.zalo.webhookSecret)
            .update(signatureData)
            .digest('hex');
        if (!crypto_1.default.timingSafeEqual(Buffer.from(signature), Buffer.from(expectedSignature))) {
            throw new errorHandler_1.AppError('Invalid webhook signature', 401);
        }
        logger_1.logger.info('Zalo webhook signature verified successfully');
        next();
    }
    catch (error) {
        logger_1.logger.error('Zalo webhook signature verification failed:', error);
        next(error);
    }
};
exports.verifyZaloSignature = verifyZaloSignature;
const validateZaloPayload = (req, res, next) => {
    try {
        const { body } = req;
        if (!body) {
            throw new errorHandler_1.AppError('Empty request body', 400);
        }
        if (!body.app_id) {
            throw new errorHandler_1.AppError('Missing app_id in webhook payload', 400);
        }
        if (!body.timestamp) {
            throw new errorHandler_1.AppError('Missing timestamp in webhook payload', 400);
        }
        if (!body.event_name && !body.message) {
            throw new errorHandler_1.AppError('Missing event_name or message in webhook payload', 400);
        }
        logger_1.logger.info('Zalo webhook payload validation passed', {
            app_id: body.app_id,
            event_name: body.event_name,
            user_id: body.user_id_by_app
        });
        next();
    }
    catch (error) {
        logger_1.logger.error('Zalo webhook payload validation failed:', error);
        next(error);
    }
};
exports.validateZaloPayload = validateZaloPayload;
//# sourceMappingURL=zaloAuth.js.map