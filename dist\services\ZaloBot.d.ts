import { ZaloConfig } from '../types';
export declare class ZaloBot {
    private config;
    private isInitialized;
    private zalo;
    private api;
    constructor(config: ZaloConfig);
    initialize(): Promise<void>;
    private setupMessageListener;
    private handleMessage;
    private handleTextMessage;
    sendTextMessage(threadId: string, text: string, threadType?: any): Promise<void>;
    sendSticker(threadId: string, stickerId: string, threadType?: any): Promise<void>;
    sendMessageWithStyle(threadId: string, text: string, styles: any[], threadType?: any): Promise<void>;
    getOwnId(): Promise<string>;
    isReady(): boolean;
}
