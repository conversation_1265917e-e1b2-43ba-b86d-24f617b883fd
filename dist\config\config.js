"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.config = void 0;
exports.validateConfig = validateConfig;
exports.config = {
    app: {
        name: 'Zalo Chatbot',
        version: '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        port: parseInt(process.env.PORT || '3000', 10),
        apiPrefix: process.env.API_PREFIX || '/api/v1',
    },
    zalo: {
        cookie: process.env.ZALO_COOKIE || '',
        userAgent: process.env.ZALO_USER_AGENT || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        webhookUrl: process.env.WEBHOOK_URL || '',
        webhookSecret: process.env.WEBHOOK_SECRET || '',
    },
    logging: {
        level: process.env.LOG_LEVEL || 'info',
        format: process.env.LOG_FORMAT || 'combined',
    },
};
function validateConfig() {
}
//# sourceMappingURL=config.js.map