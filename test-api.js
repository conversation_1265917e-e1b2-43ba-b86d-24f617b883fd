// Simple test script để kiểm tra API
const http = require('http');

const API_BASE = 'http://localhost:3000';

// Test health endpoint
function testHealth() {
  return new Promise((resolve, reject) => {
    const req = http.get(`${API_BASE}/health`, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        console.log('✅ Health Check:', JSON.parse(data));
        resolve();
      });
    });
    req.on('error', reject);
  });
}

// Test API health endpoint
function testApiHealth() {
  return new Promise((resolve, reject) => {
    const req = http.get(`${API_BASE}/api/v1/health`, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        console.log('✅ API Health Check:', JSON.parse(data));
        resolve();
      });
    });
    req.on('error', reject);
  });
}

// Test webhook endpoint với sample data
function testWebhook() {
  return new Promise((resolve, reject) => {
    const sampleData = JSON.stringify({
      app_id: "test_app_id",
      user_id_by_app: "test_user_123",
      event_name: "user_send_text",
      message: {
        message_id: "msg_123",
        from_id: "test_user_123",
        to_id: "bot_id",
        message: {
          text: "Hello from test!"
        },
        timestamp: Date.now()
      },
      timestamp: Math.floor(Date.now() / 1000)
    });

    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/v1/webhook',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(sampleData)
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        console.log('✅ Webhook Test:', JSON.parse(data));
        resolve();
      });
    });

    req.on('error', reject);
    req.write(sampleData);
    req.end();
  });
}

// Test webhook verification
function testWebhookVerification() {
  return new Promise((resolve, reject) => {
    const req = http.get(`${API_BASE}/api/v1/webhook?hub.challenge=test_challenge&hub.verify_token=test_token`, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        console.log('✅ Webhook Verification:', data);
        resolve();
      });
    });
    req.on('error', reject);
  });
}

// Run all tests
async function runTests() {
  console.log('🚀 Starting API Tests...\n');
  
  try {
    await testHealth();
    await testApiHealth();
    await testWebhookVerification();
    await testWebhook();
    
    console.log('\n✅ All tests passed!');
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.log('\n💡 Make sure the server is running: yarn dev:api');
  }
}

runTests();
