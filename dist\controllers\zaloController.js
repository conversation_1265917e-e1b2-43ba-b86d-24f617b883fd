"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ZaloController = void 0;
const logger_1 = require("../utils/logger");
const errorHandler_1 = require("../middleware/errorHandler");
const ZaloMessageService_1 = require("../services/ZaloMessageService");
class ZaloController {
    static handleWebhook = (0, errorHandler_1.asyncHandler)(async (req, res) => {
        try {
            const webhookData = req.body;
            logger_1.logger.info('Received Zalo webhook:', {
                app_id: webhookData.app_id,
                event_name: webhookData.event_name,
                user_id: webhookData.user_id_by_app,
                timestamp: webhookData.timestamp
            });
            switch (webhookData.event_name) {
                case 'user_send_text':
                    await ZaloController.handleTextMessage(webhookData);
                    break;
                case 'user_send_image':
                    await ZaloController.handleImageMessage(webhookData);
                    break;
                case 'user_send_file':
                    await ZaloController.handleFileMessage(webhookData);
                    break;
                case 'user_send_audio':
                    await ZaloController.handleAudioMessage(webhookData);
                    break;
                case 'user_send_video':
                    await ZaloController.handleVideoMessage(webhookData);
                    break;
                case 'user_send_location':
                    await ZaloController.handleLocationMessage(webhookData);
                    break;
                case 'user_send_sticker':
                    await ZaloController.handleStickerMessage(webhookData);
                    break;
                case 'follow':
                    await ZaloController.handleFollowEvent(webhookData);
                    break;
                case 'unfollow':
                    await ZaloController.handleUnfollowEvent(webhookData);
                    break;
                default:
                    logger_1.logger.warn(`Unhandled event type: ${webhookData.event_name}`);
            }
            res.status(200).json({
                status: 'ok',
                message: 'Webhook processed successfully',
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            logger_1.logger.error('Error processing Zalo webhook:', error);
            throw new errorHandler_1.AppError('Failed to process webhook', 500);
        }
    });
    static async handleTextMessage(data) {
        const messageInfo = ZaloMessageService_1.ZaloMessageService.extractMessageInfo(data);
        if (messageInfo && messageInfo.messageType === 'text') {
            await ZaloMessageService_1.ZaloMessageService.processTextMessage(messageInfo.userId, messageInfo.content, messageInfo.timestamp);
        }
    }
    static async handleImageMessage(data) {
        const messageInfo = ZaloMessageService_1.ZaloMessageService.extractMessageInfo(data);
        if (messageInfo && messageInfo.messageType === 'image') {
            await ZaloMessageService_1.ZaloMessageService.processImageMessage(messageInfo.userId, messageInfo.content, messageInfo.timestamp);
        }
    }
    static async handleFileMessage(data) {
        logger_1.logger.info('Processing file message:', {
            user_id: data.user_id_by_app,
            attachment: data.message?.message?.attachment,
            timestamp: data.timestamp
        });
    }
    static async handleAudioMessage(data) {
        logger_1.logger.info('Processing audio message:', {
            user_id: data.user_id_by_app,
            attachment: data.message?.message?.attachment,
            timestamp: data.timestamp
        });
    }
    static async handleVideoMessage(data) {
        logger_1.logger.info('Processing video message:', {
            user_id: data.user_id_by_app,
            attachment: data.message?.message?.attachment,
            timestamp: data.timestamp
        });
    }
    static async handleLocationMessage(data) {
        logger_1.logger.info('Processing location message:', {
            user_id: data.user_id_by_app,
            attachment: data.message?.message?.attachment,
            timestamp: data.timestamp
        });
    }
    static async handleStickerMessage(data) {
        logger_1.logger.info('Processing sticker message:', {
            user_id: data.user_id_by_app,
            attachment: data.message?.message?.attachment,
            timestamp: data.timestamp
        });
    }
    static async handleFollowEvent(data) {
        await ZaloMessageService_1.ZaloMessageService.processFollowEvent(data.user_id_by_app, 'follow', data.timestamp);
    }
    static async handleUnfollowEvent(data) {
        await ZaloMessageService_1.ZaloMessageService.processFollowEvent(data.user_id_by_app, 'unfollow', data.timestamp);
    }
    static testEndpoint = (0, errorHandler_1.asyncHandler)(async (req, res) => {
        res.status(200).json({
            message: 'Zalo API is working!',
            timestamp: new Date().toISOString(),
            environment: process.env.NODE_ENV || 'development'
        });
    });
}
exports.ZaloController = ZaloController;
//# sourceMappingURL=zaloController.js.map