import { Request, Response, NextFunction } from 'express';
export interface ZaloWebhookRequest extends Request {
    rawBody?: Buffer;
}
export declare const rawBodyParser: (req: ZaloWebhookRequest, res: Response, next: NextFunction) => void;
export declare const verifyZaloSignature: (req: ZaloWebhookRequest, res: Response, next: NextFunction) => void;
export declare const validateZaloPayload: (req: Request, res: Response, next: NextFunction) => void;
