"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.forbiddenError = exports.unauthorizedError = exports.notFoundError = exports.validationError = exports.asyncHandler = exports.errorHandler = exports.AppError = void 0;
const logger_1 = require("../utils/logger");
class AppError extends Error {
    statusCode;
    isOperational;
    constructor(message, statusCode = 500, isOperational = true) {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = isOperational;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.AppError = AppError;
const errorHandler = (error, req, res, next) => {
    const statusCode = error.statusCode || 500;
    const message = error.message || 'Internal Server Error';
    logger_1.logger.error(`Error ${statusCode}: ${message}`, {
        error: error.stack,
        url: req.url,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent')
    });
    res.status(statusCode).json({
        error: {
            message,
            status: statusCode,
            timestamp: new Date().toISOString(),
            path: req.path
        }
    });
};
exports.errorHandler = errorHandler;
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
exports.asyncHandler = asyncHandler;
const validationError = (message) => {
    return new AppError(message, 400);
};
exports.validationError = validationError;
const notFoundError = (resource) => {
    return new AppError(`${resource} not found`, 404);
};
exports.notFoundError = notFoundError;
const unauthorizedError = (message = 'Unauthorized') => {
    return new AppError(message, 401);
};
exports.unauthorizedError = unauthorizedError;
const forbiddenError = (message = 'Forbidden') => {
    return new AppError(message, 403);
};
exports.forbiddenError = forbiddenError;
//# sourceMappingURL=errorHandler.js.map