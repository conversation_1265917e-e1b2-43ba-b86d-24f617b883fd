import { ZaloBot } from '../services/ZaloBot';
import { config } from '../config/config';
import { logger } from '../utils/logger';

// Declare require for Node.js
declare const require: any;

// Import zca-js types
const { ThreadType, Urgency, TextStyle } = require('zca-js');

/**
 * Ví dụ về các tính năng nâng cao của Zalo Bot
 */
export class AdvancedZaloBot extends ZaloBot {
  
  // Override handleTextMessage để thêm tính năng nâng cao
  protected async handleTextMessage(message: any): Promise<void> {
    const messageText = message.data.content.toLowerCase();
    const threadId = message.threadId;
    const threadType = message.type;

    logger.info(`Processing advanced message: ${messageText}`);

    try {
      // Xử lý các lệnh đặc biệt
      if (messageText.startsWith('/')) {
        await this.handleCommand(messageText, threadId, threadType);
        return;
      }

      // X<PERSON> lý tin nhắn thông thường
      await this.handleNormalMessage(messageText, threadId, threadType, message);
      
    } catch (error) {
      logger.error('Error in advanced message handling:', error);
      await this.sendTextMessage(threadId, 'Xin lỗi, đã có lỗi xảy ra khi xử lý tin nhắn của bạn.', threadType);
    }
  }

  private async handleCommand(command: string, threadId: string, threadType: any): Promise<void> {
    const [cmd, ...args] = command.split(' ');

    switch (cmd) {
      case '/help':
        await this.sendHelpMessage(threadId, threadType);
        break;
        
      case '/style':
        await this.sendStyledMessage(threadId, threadType);
        break;
        
      case '/urgent':
        await this.sendUrgentMessage(threadId, threadType);
        break;
        
      case '/sticker':
        const stickerId = args[0] || '1';
        await this.sendSticker(threadId, stickerId, threadType);
        break;
        
      case '/info':
        await this.sendBotInfo(threadId, threadType);
        break;
        
      case '/time':
        await this.sendCurrentTime(threadId, threadType);
        break;
        
      default:
        await this.sendTextMessage(threadId, `Lệnh "${cmd}" không được hỗ trợ. Gõ /help để xem danh sách lệnh.`, threadType);
    }
  }

  private async handleNormalMessage(messageText: string, threadId: string, threadType: any, message: any): Promise<void> {
    // Phản hồi thông minh dựa trên nội dung
    if (messageText.includes('hello') || messageText.includes('hi') || messageText.includes('xin chào')) {
      await this.sendWelcomeMessage(threadId, threadType);
    } else if (messageText.includes('bye') || messageText.includes('tạm biệt')) {
      await this.sendGoodbyeMessage(threadId, threadType);
    } else if (messageText.includes('cảm ơn') || messageText.includes('thank')) {
      await this.sendTextMessage(threadId, 'Không có gì! Tôi luôn sẵn sàng giúp đỡ bạn 😊', threadType);
    } else if (messageText.includes('thời tiết') || messageText.includes('weather')) {
      await this.sendWeatherInfo(threadId, threadType);
    } else {
      // Echo với phản hồi thông minh
      await this.sendSmartEcho(messageText, threadId, threadType);
    }
  }

  private async sendHelpMessage(threadId: string, threadType: any): Promise<void> {
    const helpText = `🤖 **Danh sách lệnh Bot Zalo:**

📝 **Lệnh cơ bản:**
• /help - Hiển thị trợ giúp
• /info - Thông tin bot
• /time - Thời gian hiện tại

🎨 **Lệnh định dạng:**
• /style - Ví dụ tin nhắn có định dạng
• /urgent - Tin nhắn quan trọng

😄 **Lệnh giải trí:**
• /sticker [id] - Gửi sticker

💬 **Tin nhắn thông thường:**
• Gửi "hello" để chào hỏi
• Gửi "thời tiết" để xem thông tin thời tiết
• Bot sẽ trả lời thông minh các tin nhắn khác`;

    await this.sendTextMessage(threadId, helpText, threadType);
  }

  private async sendStyledMessage(threadId: string, threadType: any): Promise<void> {
    const text = "Đây là tin nhắn có ĐỊNH DẠNG đặc biệt!";
    
    const styles = [
      {
        start: 17,
        len: 10,
        st: TextStyle.Bold
      },
      {
        start: 17,
        len: 10,
        st: TextStyle.Red
      },
      {
        start: 28,
        len: 8,
        st: TextStyle.Italic
      },
      {
        start: 28,
        len: 8,
        st: TextStyle.Blue
      }
    ];

    await this.sendMessageWithStyle(threadId, text, styles, threadType);
  }

  private async sendUrgentMessage(threadId: string, threadType: any): Promise<void> {
    const text = "⚠️ TIN NHẮN QUAN TRỌNG ⚠️";
    
    const styles = [
      {
        start: 0,
        len: text.length,
        st: TextStyle.Bold
      },
      {
        start: 0,
        len: text.length,
        st: TextStyle.Red
      },
      {
        start: 0,
        len: text.length,
        st: TextStyle.Big
      }
    ];

    const messageContent = {
      msg: text,
      urgency: Urgency.Urgent,
      styles: styles
    };

    // Sử dụng API trực tiếp để gửi tin nhắn urgent
    await (this as any).api.sendMessage(messageContent, threadId, threadType);
  }

  private async sendBotInfo(threadId: string, threadType: any): Promise<void> {
    const ownId = await this.getOwnId();
    const infoText = `🤖 **Thông tin Bot:**

• **ID:** ${ownId}
• **Tên:** Zalo Chatbot v1.0
• **Trạng thái:** ${this.isReady() ? '✅ Hoạt động' : '❌ Offline'}
• **Framework:** zca-js
• **Ngôn ngữ:** TypeScript/Node.js

💡 Gõ /help để xem danh sách lệnh!`;

    await this.sendTextMessage(threadId, infoText, threadType);
  }

  private async sendCurrentTime(threadId: string, threadType: any): Promise<void> {
    const now = new Date();
    const timeText = `🕐 **Thời gian hiện tại:**

📅 **Ngày:** ${now.toLocaleDateString('vi-VN')}
⏰ **Giờ:** ${now.toLocaleTimeString('vi-VN')}
🌍 **Múi giờ:** GMT+7 (Việt Nam)`;

    await this.sendTextMessage(threadId, timeText, threadType);
  }

  private async sendWelcomeMessage(threadId: string, threadType: any): Promise<void> {
    const welcomeText = `👋 **Xin chào!**

Tôi là Bot Zalo thông minh! 🤖

✨ **Tôi có thể:**
• Trả lời tin nhắn thông minh
• Xử lý các lệnh đặc biệt
• Gửi tin nhắn có định dạng
• Và nhiều tính năng khác!

💡 Gõ **/help** để xem danh sách lệnh đầy đủ!`;

    await this.sendTextMessage(threadId, welcomeText, threadType);
  }

  private async sendGoodbyeMessage(threadId: string, threadType: any): Promise<void> {
    await this.sendTextMessage(threadId, '👋 Tạm biệt! Hẹn gặp lại bạn sau nhé! 😊', threadType);
  }

  private async sendWeatherInfo(threadId: string, threadType: any): Promise<void> {
    // Đây là ví dụ - trong thực tế bạn có thể tích hợp API thời tiết thực
    const weatherText = `🌤️ **Thông tin thời tiết:**

📍 **Hà Nội:**
🌡️ Nhiệt độ: 28°C
💧 Độ ẩm: 65%
🌬️ Gió: 15 km/h
☁️ Trời: Có mây

💡 *Đây là dữ liệu mẫu. Tích hợp API thời tiết thực để có thông tin chính xác!*`;

    await this.sendTextMessage(threadId, weatherText, threadType);
  }

  private async sendSmartEcho(messageText: string, threadId: string, threadType: any): Promise<void> {
    const responses = [
      `Tôi hiểu bạn nói: "${messageText}" 🤔`,
      `Thú vị! Bạn vừa nói: "${messageText}" 😊`,
      `Cảm ơn bạn đã chia sẻ: "${messageText}" 👍`,
      `Tôi đã ghi nhận: "${messageText}" ✅`
    ];

    const randomResponse = responses[Math.floor(Math.random() * responses.length)];
    await this.sendTextMessage(threadId, randomResponse, threadType);
  }
}

// Export để sử dụng
export default AdvancedZaloBot;
