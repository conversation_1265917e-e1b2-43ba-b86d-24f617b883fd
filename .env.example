# Application Configuration
NODE_ENV=development
PORT=3000
API_PREFIX=/api/v1
LOG_LEVEL=info
LOG_FORMAT=combined

# Zalo Configuration
ZALO_COOKIE=
ZALO_USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
WEBHOOK_URL=https://your-domain.com/api/v1/webhook
WEBHOOK_SECRET=your-webhook-secret-key
ZALO_VERIFY_TOKEN=your-verify-token

# Bot Configuration
ENABLE_ZALO_BOT=true

# Security
ALLOWED_ORIGINS=http://localhost:3000,https://your-domain.com

# External APIs (if needed)
# OPENAI_API_KEY=
# GOOGLE_API_KEY=
