"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ZaloBot = void 0;
const logger_1 = require("../utils/logger");
const { Zalo, ThreadType, Urgency, TextStyle } = require('zca-js');
class ZaloBot {
    config;
    isInitialized = false;
    zalo;
    api;
    constructor(config) {
        this.config = config;
    }
    async initialize() {
        try {
            logger_1.logger.info('Initializing Zalo Bot...');
            this.zalo = new Zalo({
                selfListen: false,
            });
            if (this.config.cookie) {
                logger_1.logger.info('Logging in with cookie...');
                this.api = await this.zalo.login(this.config.cookie, this.config.userAgent);
            }
            else {
                logger_1.logger.info('No cookie provided. Using QR code login...');
                this.api = await this.zalo.loginQR();
            }
            this.setupMessageListener();
            this.isInitialized = true;
            logger_1.logger.info('Zalo Bot initialized successfully');
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize Zalo Bot:', error);
            throw error;
        }
    }
    setupMessageListener() {
        const { listener } = this.api;
        listener.on('message', (message) => {
            this.handleMessage(message);
        });
        listener.on('reaction', (reaction) => {
            logger_1.logger.info('Received reaction:', reaction);
        });
        listener.on('group_event', (event) => {
            logger_1.logger.info('Received group event:', event);
        });
        listener.start();
        logger_1.logger.info('Message listener started');
    }
    async handleMessage(message) {
        try {
            logger_1.logger.info(`Received message from ${message.senderID}: ${message.data.content}`);
            if (message.isSelf) {
                return;
            }
            const isPlainText = typeof message.data.content === 'string';
            if (isPlainText) {
                await this.handleTextMessage(message);
            }
            else {
                logger_1.logger.info('Received non-text message:', message.data);
            }
        }
        catch (error) {
            logger_1.logger.error('Error handling message:', error);
        }
    }
    async handleTextMessage(message) {
        const messageText = message.data.content;
        const threadId = message.threadId;
        const threadType = message.type;
        logger_1.logger.info(`Processing text message: ${messageText}`);
        if (messageText.toLowerCase().includes('hello') || messageText.toLowerCase().includes('hi')) {
            await this.sendTextMessage(threadId, 'Xin chào! Tôi là bot Zalo. Tôi có thể giúp gì cho bạn?', threadType);
        }
        else if (messageText.toLowerCase().includes('help')) {
            await this.sendTextMessage(threadId, 'Tôi có thể:\n- Trả lời tin nhắn\n- Gửi sticker\n- Xử lý hình ảnh\nHãy thử gửi "hello" để bắt đầu!', threadType);
        }
        else {
            await this.sendTextMessage(threadId, `Bạn vừa nói: ${messageText}`, threadType);
        }
    }
    async sendTextMessage(threadId, text, threadType) {
        try {
            logger_1.logger.info(`Sending text message to ${threadId}: ${text}`);
            const result = await this.api.sendMessage(text, threadId, threadType);
            if (result && result.message) {
                logger_1.logger.info(`Message sent successfully with ID: ${result.message.msgId}`);
            }
            else {
                logger_1.logger.info('Message sent successfully');
            }
        }
        catch (error) {
            logger_1.logger.error('Failed to send message:', error);
            throw error;
        }
    }
    async sendSticker(threadId, stickerId, threadType) {
        try {
            logger_1.logger.info(`Sending sticker ${stickerId} to ${threadId}`);
            await this.api.sendSticker(stickerId, threadId, threadType);
            logger_1.logger.info('Sticker sent successfully');
        }
        catch (error) {
            logger_1.logger.error('Failed to send sticker:', error);
            throw error;
        }
    }
    async sendMessageWithStyle(threadId, text, styles, threadType) {
        try {
            logger_1.logger.info(`Sending styled message to ${threadId}: ${text}`);
            const messageContent = {
                msg: text,
                styles: styles
            };
            await this.api.sendMessage(messageContent, threadId, threadType);
            logger_1.logger.info('Styled message sent successfully');
        }
        catch (error) {
            logger_1.logger.error('Failed to send styled message:', error);
            throw error;
        }
    }
    async getOwnId() {
        try {
            return await this.api.getOwnId();
        }
        catch (error) {
            logger_1.logger.error('Failed to get own ID:', error);
            throw error;
        }
    }
    isReady() {
        return this.isInitialized && this.api;
    }
}
exports.ZaloBot = ZaloBot;
//# sourceMappingURL=ZaloBot.js.map