import { Request, Response } from 'express';
export declare class ZaloController {
    static handleWebhook: (req: Request, res: Response, next: import("express").NextFunction) => void;
    private static handleTextMessage;
    private static handleImageMessage;
    private static handleFileMessage;
    private static handleAudioMessage;
    private static handleVideoMessage;
    private static handleLocationMessage;
    private static handleStickerMessage;
    private static handleFollowEvent;
    private static handleUnfollowEvent;
    static testEndpoint: (req: Request, res: Response, next: import("express").NextFunction) => void;
}
