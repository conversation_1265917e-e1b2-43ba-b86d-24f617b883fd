{"version": 3, "file": "advanced-features.js", "sourceRoot": "", "sources": ["../../src/examples/advanced-features.ts"], "names": [], "mappings": ";;;AAAA,iDAA8C;AAE9C,4CAAyC;AAMzC,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;AAK7D,MAAa,eAAgB,SAAQ,iBAAO;IAGnC,KAAK,CAAC,yBAAyB,CAAC,OAAY;QACjD,MAAM,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QACvD,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QAClC,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC;QAEhC,eAAM,CAAC,IAAI,CAAC,gCAAgC,WAAW,EAAE,CAAC,CAAC;QAE3D,IAAI,CAAC;YAEH,IAAI,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBAChC,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;gBAC5D,OAAO;YACT,CAAC;YAGD,MAAM,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;QAE7E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC3D,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,uDAAuD,EAAE,UAAU,CAAC,CAAC;QAC5G,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,OAAe,EAAE,QAAgB,EAAE,UAAe;QAC5E,MAAM,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAE1C,QAAQ,GAAG,EAAE,CAAC;YACZ,KAAK,OAAO;gBACV,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;gBACjD,MAAM;YAER,KAAK,QAAQ;gBACX,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;gBACnD,MAAM;YAER,KAAK,SAAS;gBACZ,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;gBACnD,MAAM;YAER,KAAK,UAAU;gBACb,MAAM,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;gBACjC,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;gBACxD,MAAM;YAER,KAAK,OAAO;gBACV,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;gBAC7C,MAAM;YAER,KAAK,OAAO;gBACV,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;gBACjD,MAAM;YAER;gBACE,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,SAAS,GAAG,sDAAsD,EAAE,UAAU,CAAC,CAAC;QACzH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,WAAmB,EAAE,QAAgB,EAAE,UAAe,EAAE,OAAY;QAEpG,IAAI,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACpG,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QACtD,CAAC;aAAM,IAAI,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAC3E,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QACtD,CAAC;aAAM,IAAI,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAC3E,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,+CAA+C,EAAE,UAAU,CAAC,CAAC;QACpG,CAAC;aAAM,IAAI,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAChF,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QACnD,CAAC;aAAM,CAAC;YAEN,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,QAAgB,EAAE,UAAe;QAC7D,MAAM,QAAQ,GAAG;;;;;;;;;;;;;;;;;8CAiByB,CAAC;QAE3C,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;IAC7D,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,QAAgB,EAAE,UAAe;QAC/D,MAAM,IAAI,GAAG,wCAAwC,CAAC;QAEtD,MAAM,MAAM,GAAG;YACb;gBACE,KAAK,EAAE,EAAE;gBACT,GAAG,EAAE,EAAE;gBACP,EAAE,EAAE,SAAS,CAAC,IAAI;aACnB;YACD;gBACE,KAAK,EAAE,EAAE;gBACT,GAAG,EAAE,EAAE;gBACP,EAAE,EAAE,SAAS,CAAC,GAAG;aAClB;YACD;gBACE,KAAK,EAAE,EAAE;gBACT,GAAG,EAAE,CAAC;gBACN,EAAE,EAAE,SAAS,CAAC,MAAM;aACrB;YACD;gBACE,KAAK,EAAE,EAAE;gBACT,GAAG,EAAE,CAAC;gBACN,EAAE,EAAE,SAAS,CAAC,IAAI;aACnB;SACF,CAAC;QAEF,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;IACtE,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,QAAgB,EAAE,UAAe;QAC/D,MAAM,IAAI,GAAG,2BAA2B,CAAC;QAEzC,MAAM,MAAM,GAAG;YACb;gBACE,KAAK,EAAE,CAAC;gBACR,GAAG,EAAE,IAAI,CAAC,MAAM;gBAChB,EAAE,EAAE,SAAS,CAAC,IAAI;aACnB;YACD;gBACE,KAAK,EAAE,CAAC;gBACR,GAAG,EAAE,IAAI,CAAC,MAAM;gBAChB,EAAE,EAAE,SAAS,CAAC,GAAG;aAClB;YACD;gBACE,KAAK,EAAE,CAAC;gBACR,GAAG,EAAE,IAAI,CAAC,MAAM;gBAChB,EAAE,EAAE,SAAS,CAAC,GAAG;aAClB;SACF,CAAC;QAEF,MAAM,cAAc,GAAG;YACrB,GAAG,EAAE,IAAI;YACT,OAAO,EAAE,OAAO,CAAC,MAAM;YACvB,MAAM,EAAE,MAAM;SACf,CAAC;QAGF,MAAO,IAAY,CAAC,GAAG,CAAC,WAAW,CAAC,cAAc,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;IAC5E,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,QAAgB,EAAE,UAAe;QACzD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;QACpC,MAAM,QAAQ,GAAG;;YAET,KAAK;;oBAEG,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,WAAW;;;;mCAI7B,CAAC;QAEhC,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;IAC7D,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,QAAgB,EAAE,UAAe;QAC7D,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,QAAQ,GAAG;;eAEN,GAAG,CAAC,kBAAkB,CAAC,OAAO,CAAC;aACjC,GAAG,CAAC,kBAAkB,CAAC,OAAO,CAAC;iCACX,CAAC;QAE9B,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;IAC7D,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,QAAgB,EAAE,UAAe;QAChE,MAAM,WAAW,GAAG;;;;;;;;;;8CAUsB,CAAC;QAE3C,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;IAChE,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,QAAgB,EAAE,UAAe;QAChE,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,0CAA0C,EAAE,UAAU,CAAC,CAAC;IAC/F,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,QAAgB,EAAE,UAAe;QAE7D,MAAM,WAAW,GAAG;;;;;;;;gFAQwD,CAAC;QAE7E,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;IAChE,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,WAAmB,EAAE,QAAgB,EAAE,UAAe;QAChF,MAAM,SAAS,GAAG;YAChB,sBAAsB,WAAW,MAAM;YACvC,yBAAyB,WAAW,MAAM;YAC1C,2BAA2B,WAAW,MAAM;YAC5C,qBAAqB,WAAW,KAAK;SACtC,CAAC;QAEF,MAAM,cAAc,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;QAC/E,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,cAAc,EAAE,UAAU,CAAC,CAAC;IACnE,CAAC;CACF;AAvOD,0CAuOC;AAGD,kBAAe,eAAe,CAAC"}