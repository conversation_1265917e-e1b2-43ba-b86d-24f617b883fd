import { Request, Response } from 'express';
import { logger } from '../utils/logger';
import { ZaloWebhookEvent } from '../types/zalo';
import { asyncHandler, AppError } from '../middleware/errorHandler';
import { ZaloMessageService } from '../services/ZaloMessageService';

export class ZaloController {
  // Xử lý webhook từ Zalo
  public static handleWebhook = asyncHandler(async (req: Request, res: Response) => {
    try {
      const webhookData: ZaloWebhookEvent = req.body;
      
      logger.info('Received Zalo webhook:', {
        app_id: webhookData.app_id,
        event_name: webhookData.event_name,
        user_id: webhookData.user_id_by_app,
        timestamp: webhookData.timestamp
      });

      // Xử lý các loại event khác nhau
      switch (webhookData.event_name) {
        case 'user_send_text':
          await ZaloController.handleTextMessage(webhookData);
          break;
        
        case 'user_send_image':
          await <PERSON>alo<PERSON><PERSON>roller.handleImageMessage(webhookData);
          break;
        
        case 'user_send_file':
          await ZaloController.handleFileMessage(webhookData);
          break;
        
        case 'user_send_audio':
          await ZaloController.handleAudioMessage(webhookData);
          break;
        
        case 'user_send_video':
          await ZaloController.handleVideoMessage(webhookData);
          break;
        
        case 'user_send_location':
          await ZaloController.handleLocationMessage(webhookData);
          break;
        
        case 'user_send_sticker':
          await ZaloController.handleStickerMessage(webhookData);
          break;
        
        case 'follow':
          await ZaloController.handleFollowEvent(webhookData);
          break;
        
        case 'unfollow':
          await ZaloController.handleUnfollowEvent(webhookData);
          break;
        
        default:
          logger.warn(`Unhandled event type: ${webhookData.event_name}`);
      }

      // Trả về response thành công cho Zalo
      res.status(200).json({
        status: 'ok',
        message: 'Webhook processed successfully',
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('Error processing Zalo webhook:', error);
      throw new AppError('Failed to process webhook', 500);
    }
  });

  // Xử lý tin nhắn văn bản
  private static async handleTextMessage(data: ZaloWebhookEvent): Promise<void> {
    const messageInfo = ZaloMessageService.extractMessageInfo(data);
    if (messageInfo && messageInfo.messageType === 'text') {
      await ZaloMessageService.processTextMessage(
        messageInfo.userId,
        messageInfo.content,
        messageInfo.timestamp
      );
    }
  }

  // Xử lý tin nhắn hình ảnh
  private static async handleImageMessage(data: ZaloWebhookEvent): Promise<void> {
    const messageInfo = ZaloMessageService.extractMessageInfo(data);
    if (messageInfo && messageInfo.messageType === 'image') {
      await ZaloMessageService.processImageMessage(
        messageInfo.userId,
        messageInfo.content,
        messageInfo.timestamp
      );
    }
  }

  // Xử lý tin nhắn file
  private static async handleFileMessage(data: ZaloWebhookEvent): Promise<void> {
    logger.info('Processing file message:', {
      user_id: data.user_id_by_app,
      attachment: data.message?.message?.attachment,
      timestamp: data.timestamp
    });

    // TODO: Implement file message processing logic
  }

  // Xử lý tin nhắn audio
  private static async handleAudioMessage(data: ZaloWebhookEvent): Promise<void> {
    logger.info('Processing audio message:', {
      user_id: data.user_id_by_app,
      attachment: data.message?.message?.attachment,
      timestamp: data.timestamp
    });

    // TODO: Implement audio message processing logic
  }

  // Xử lý tin nhắn video
  private static async handleVideoMessage(data: ZaloWebhookEvent): Promise<void> {
    logger.info('Processing video message:', {
      user_id: data.user_id_by_app,
      attachment: data.message?.message?.attachment,
      timestamp: data.timestamp
    });

    // TODO: Implement video message processing logic
  }

  // Xử lý tin nhắn vị trí
  private static async handleLocationMessage(data: ZaloWebhookEvent): Promise<void> {
    logger.info('Processing location message:', {
      user_id: data.user_id_by_app,
      attachment: data.message?.message?.attachment,
      timestamp: data.timestamp
    });

    // TODO: Implement location message processing logic
  }

  // Xử lý tin nhắn sticker
  private static async handleStickerMessage(data: ZaloWebhookEvent): Promise<void> {
    logger.info('Processing sticker message:', {
      user_id: data.user_id_by_app,
      attachment: data.message?.message?.attachment,
      timestamp: data.timestamp
    });

    // TODO: Implement sticker message processing logic
  }

  // Xử lý event follow
  private static async handleFollowEvent(data: ZaloWebhookEvent): Promise<void> {
    await ZaloMessageService.processFollowEvent(
      data.user_id_by_app,
      'follow',
      data.timestamp
    );
  }

  // Xử lý event unfollow
  private static async handleUnfollowEvent(data: ZaloWebhookEvent): Promise<void> {
    await ZaloMessageService.processFollowEvent(
      data.user_id_by_app,
      'unfollow',
      data.timestamp
    );
  }

  // Test endpoint để kiểm tra API
  public static testEndpoint = asyncHandler(async (req: Request, res: Response) => {
    res.status(200).json({
      message: 'Zalo API is working!',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development'
    });
  });
}
