import { ZaloWebhookEvent } from '../types/zalo';
export declare class ZaloMessageService {
    static processTextMessage(userId: string, message: string, timestamp: number): Promise<void>;
    static processImageMessage(userId: string, imageUrl: string, timestamp: number): Promise<void>;
    static processFileMessage(userId: string, fileUrl: string, fileName?: string, timestamp?: number): Promise<void>;
    static processFollowEvent(userId: string, eventType: 'follow' | 'unfollow', timestamp: number): Promise<void>;
    private static saveMessage;
    private static generateResponse;
    private static sendTextMessage;
    static extractMessageInfo(webhookEvent: ZaloWebhookEvent): {
        userId: string;
        messageType: string;
        content: string;
        timestamp: number;
    } | null;
}
