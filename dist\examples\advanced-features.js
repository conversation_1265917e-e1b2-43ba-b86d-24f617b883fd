"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdvancedZaloBot = void 0;
const ZaloBot_1 = require("../services/ZaloBot");
const logger_1 = require("../utils/logger");
const { ThreadType, Urgency, TextStyle } = require('zca-js');
class AdvancedZaloBot extends ZaloBot_1.ZaloBot {
    async handleAdvancedTextMessage(message) {
        const messageText = message.data.content.toLowerCase();
        const threadId = message.threadId;
        const threadType = message.type;
        logger_1.logger.info(`Processing advanced message: ${messageText}`);
        try {
            if (messageText.startsWith('/')) {
                await this.handleCommand(messageText, threadId, threadType);
                return;
            }
            await this.handleNormalMessage(messageText, threadId, threadType, message);
        }
        catch (error) {
            logger_1.logger.error('Error in advanced message handling:', error);
            await this.sendTextMessage(threadId, 'Xin lỗi, đã có lỗi xảy ra khi xử lý tin nhắn của bạn.', threadType);
        }
    }
    async handleCommand(command, threadId, threadType) {
        const [cmd, ...args] = command.split(' ');
        switch (cmd) {
            case '/help':
                await this.sendHelpMessage(threadId, threadType);
                break;
            case '/style':
                await this.sendStyledMessage(threadId, threadType);
                break;
            case '/urgent':
                await this.sendUrgentMessage(threadId, threadType);
                break;
            case '/sticker':
                const stickerId = args[0] || '1';
                await this.sendSticker(threadId, stickerId, threadType);
                break;
            case '/info':
                await this.sendBotInfo(threadId, threadType);
                break;
            case '/time':
                await this.sendCurrentTime(threadId, threadType);
                break;
            default:
                await this.sendTextMessage(threadId, `Lệnh "${cmd}" không được hỗ trợ. Gõ /help để xem danh sách lệnh.`, threadType);
        }
    }
    async handleNormalMessage(messageText, threadId, threadType, message) {
        if (messageText.includes('hello') || messageText.includes('hi') || messageText.includes('xin chào')) {
            await this.sendWelcomeMessage(threadId, threadType);
        }
        else if (messageText.includes('bye') || messageText.includes('tạm biệt')) {
            await this.sendGoodbyeMessage(threadId, threadType);
        }
        else if (messageText.includes('cảm ơn') || messageText.includes('thank')) {
            await this.sendTextMessage(threadId, 'Không có gì! Tôi luôn sẵn sàng giúp đỡ bạn 😊', threadType);
        }
        else if (messageText.includes('thời tiết') || messageText.includes('weather')) {
            await this.sendWeatherInfo(threadId, threadType);
        }
        else {
            await this.sendSmartEcho(messageText, threadId, threadType);
        }
    }
    async sendHelpMessage(threadId, threadType) {
        const helpText = `🤖 **Danh sách lệnh Bot Zalo:**

📝 **Lệnh cơ bản:**
• /help - Hiển thị trợ giúp
• /info - Thông tin bot
• /time - Thời gian hiện tại

🎨 **Lệnh định dạng:**
• /style - Ví dụ tin nhắn có định dạng
• /urgent - Tin nhắn quan trọng

😄 **Lệnh giải trí:**
• /sticker [id] - Gửi sticker

💬 **Tin nhắn thông thường:**
• Gửi "hello" để chào hỏi
• Gửi "thời tiết" để xem thông tin thời tiết
• Bot sẽ trả lời thông minh các tin nhắn khác`;
        await this.sendTextMessage(threadId, helpText, threadType);
    }
    async sendStyledMessage(threadId, threadType) {
        const text = "Đây là tin nhắn có ĐỊNH DẠNG đặc biệt!";
        const styles = [
            {
                start: 17,
                len: 10,
                st: TextStyle.Bold
            },
            {
                start: 17,
                len: 10,
                st: TextStyle.Red
            },
            {
                start: 28,
                len: 8,
                st: TextStyle.Italic
            },
            {
                start: 28,
                len: 8,
                st: TextStyle.Blue
            }
        ];
        await this.sendMessageWithStyle(threadId, text, styles, threadType);
    }
    async sendUrgentMessage(threadId, threadType) {
        const text = "⚠️ TIN NHẮN QUAN TRỌNG ⚠️";
        const styles = [
            {
                start: 0,
                len: text.length,
                st: TextStyle.Bold
            },
            {
                start: 0,
                len: text.length,
                st: TextStyle.Red
            },
            {
                start: 0,
                len: text.length,
                st: TextStyle.Big
            }
        ];
        const messageContent = {
            msg: text,
            urgency: Urgency.Urgent,
            styles: styles
        };
        await this.api.sendMessage(messageContent, threadId, threadType);
    }
    async sendBotInfo(threadId, threadType) {
        const ownId = await this.getOwnId();
        const infoText = `🤖 **Thông tin Bot:**

• **ID:** ${ownId}
• **Tên:** Zalo Chatbot v1.0
• **Trạng thái:** ${this.isReady() ? '✅ Hoạt động' : '❌ Offline'}
• **Framework:** zca-js
• **Ngôn ngữ:** TypeScript/Node.js

💡 Gõ /help để xem danh sách lệnh!`;
        await this.sendTextMessage(threadId, infoText, threadType);
    }
    async sendCurrentTime(threadId, threadType) {
        const now = new Date();
        const timeText = `🕐 **Thời gian hiện tại:**

📅 **Ngày:** ${now.toLocaleDateString('vi-VN')}
⏰ **Giờ:** ${now.toLocaleTimeString('vi-VN')}
🌍 **Múi giờ:** GMT+7 (Việt Nam)`;
        await this.sendTextMessage(threadId, timeText, threadType);
    }
    async sendWelcomeMessage(threadId, threadType) {
        const welcomeText = `👋 **Xin chào!**

Tôi là Bot Zalo thông minh! 🤖

✨ **Tôi có thể:**
• Trả lời tin nhắn thông minh
• Xử lý các lệnh đặc biệt
• Gửi tin nhắn có định dạng
• Và nhiều tính năng khác!

💡 Gõ **/help** để xem danh sách lệnh đầy đủ!`;
        await this.sendTextMessage(threadId, welcomeText, threadType);
    }
    async sendGoodbyeMessage(threadId, threadType) {
        await this.sendTextMessage(threadId, '👋 Tạm biệt! Hẹn gặp lại bạn sau nhé! 😊', threadType);
    }
    async sendWeatherInfo(threadId, threadType) {
        const weatherText = `🌤️ **Thông tin thời tiết:**

📍 **Hà Nội:**
🌡️ Nhiệt độ: 28°C
💧 Độ ẩm: 65%
🌬️ Gió: 15 km/h
☁️ Trời: Có mây

💡 *Đây là dữ liệu mẫu. Tích hợp API thời tiết thực để có thông tin chính xác!*`;
        await this.sendTextMessage(threadId, weatherText, threadType);
    }
    async sendSmartEcho(messageText, threadId, threadType) {
        const responses = [
            `Tôi hiểu bạn nói: "${messageText}" 🤔`,
            `Thú vị! Bạn vừa nói: "${messageText}" 😊`,
            `Cảm ơn bạn đã chia sẻ: "${messageText}" 👍`,
            `Tôi đã ghi nhận: "${messageText}" ✅`
        ];
        const randomResponse = responses[Math.floor(Math.random() * responses.length)];
        await this.sendTextMessage(threadId, randomResponse, threadType);
    }
}
exports.AdvancedZaloBot = AdvancedZaloBot;
exports.default = AdvancedZaloBot;
//# sourceMappingURL=advanced-features.js.map