"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ZaloMessageService = void 0;
const logger_1 = require("../utils/logger");
class ZaloMessageService {
    static async processTextMessage(userId, message, timestamp) {
        try {
            logger_1.logger.info(`Processing text message from user ${userId}: ${message}`);
            await ZaloMessageService.saveMessage(userId, 'text', message, timestamp);
            const response = await ZaloMessageService.generateResponse(message);
            if (response) {
                await ZaloMessageService.sendTextMessage(userId, response);
            }
        }
        catch (error) {
            logger_1.logger.error('Error processing text message:', error);
            throw error;
        }
    }
    static async processImageMessage(userId, imageUrl, timestamp) {
        try {
            logger_1.logger.info(`Processing image message from user ${userId}: ${imageUrl}`);
            await ZaloMessageService.saveMessage(userId, 'image', imageUrl, timestamp);
        }
        catch (error) {
            logger_1.logger.error('Error processing image message:', error);
            throw error;
        }
    }
    static async processFileMessage(userId, fileUrl, fileName, timestamp) {
        try {
            logger_1.logger.info(`Processing file message from user ${userId}: ${fileName || fileUrl}`);
            await ZaloMessageService.saveMessage(userId, 'file', fileUrl, timestamp || Date.now());
        }
        catch (error) {
            logger_1.logger.error('Error processing file message:', error);
            throw error;
        }
    }
    static async processFollowEvent(userId, eventType, timestamp) {
        try {
            logger_1.logger.info(`Processing ${eventType} event from user ${userId}`);
            if (eventType === 'follow') {
                const welcomeMessage = "Chào mừng bạn đến với chatbot! 👋\nTôi có thể giúp gì cho bạn?";
                await ZaloMessageService.sendTextMessage(userId, welcomeMessage);
            }
        }
        catch (error) {
            logger_1.logger.error(`Error processing ${eventType} event:`, error);
            throw error;
        }
    }
    static async saveMessage(userId, messageType, content, timestamp) {
        try {
            logger_1.logger.info('Saving message to database:', {
                userId,
                messageType,
                content: content.substring(0, 100),
                timestamp
            });
        }
        catch (error) {
            logger_1.logger.error('Error saving message to database:', error);
            throw error;
        }
    }
    static async generateResponse(message) {
        try {
            const lowerMessage = message.toLowerCase();
            if (lowerMessage.includes('hello') || lowerMessage.includes('hi') || lowerMessage.includes('chào')) {
                return 'Xin chào! Tôi có thể giúp gì cho bạn? 😊';
            }
            if (lowerMessage.includes('help') || lowerMessage.includes('giúp')) {
                return 'Tôi có thể giúp bạn:\n- Trả lời câu hỏi\n- Cung cấp thông tin\n- Hỗ trợ khách hàng\n\nHãy cho tôi biết bạn cần gì nhé!';
            }
            if (lowerMessage.includes('bye') || lowerMessage.includes('tạm biệt')) {
                return 'Tạm biệt! Hẹn gặp lại bạn! 👋';
            }
            return 'Cảm ơn bạn đã nhắn tin! Tôi đã nhận được tin nhắn của bạn. 📝';
        }
        catch (error) {
            logger_1.logger.error('Error generating response:', error);
            return null;
        }
    }
    static async sendTextMessage(userId, message) {
        try {
            logger_1.logger.info(`Sending text message to user ${userId}: ${message}`);
            logger_1.logger.info('Message sent successfully (placeholder)');
        }
        catch (error) {
            logger_1.logger.error('Error sending text message:', error);
            throw error;
        }
    }
    static extractMessageInfo(webhookEvent) {
        try {
            const userId = webhookEvent.user_id_by_app;
            const timestamp = webhookEvent.timestamp;
            const message = webhookEvent.message;
            if (!userId || !timestamp) {
                return null;
            }
            if (message?.message?.text) {
                return {
                    userId,
                    messageType: 'text',
                    content: message.message.text,
                    timestamp
                };
            }
            if (message?.message?.attachment) {
                const attachment = message.message.attachment;
                return {
                    userId,
                    messageType: attachment.type,
                    content: attachment.payload?.url || 'attachment',
                    timestamp
                };
            }
            return null;
        }
        catch (error) {
            logger_1.logger.error('Error extracting message info:', error);
            return null;
        }
    }
}
exports.ZaloMessageService = ZaloMessageService;
//# sourceMappingURL=ZaloMessageService.js.map