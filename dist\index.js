"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const ZaloBot_1 = require("./services/ZaloBot");
const app_1 = require("./app");
const config_1 = require("./config/config");
const logger_1 = require("./utils/logger");
async function main() {
    try {
        logger_1.logger.info('🚀 Starting Zalo Chatbot System...');
        logger_1.logger.info('📡 Starting Express API Server...');
        const expressApp = new app_1.ExpressApp();
        expressApp.start();
        if (process.env.ENABLE_ZALO_BOT !== 'false') {
            logger_1.logger.info('🤖 Starting Zalo Bot...');
            const bot = new ZaloBot_1.ZaloBot(config_1.config.zalo);
            await bot.initialize();
            logger_1.logger.info('✅ Zalo Bot started successfully!');
        }
        else {
            logger_1.logger.info('⏭️ Zalo Bot disabled - running API server only');
        }
        logger_1.logger.info('✅ Zalo Chatbot System started successfully!');
    }
    catch (error) {
        logger_1.logger.error('❌ Failed to start Zalo Chatbot System:', error);
        process.exit(1);
    }
}
process.on('SIGINT', () => {
    logger_1.logger.info('🛑 Shutting down gracefully...');
    process.exit(0);
});
process.on('SIGTERM', () => {
    logger_1.logger.info('🛑 Shutting down gracefully...');
    process.exit(0);
});
main().catch(error => {
    logger_1.logger.error('💥 Unhandled error:', error);
    process.exit(1);
});
//# sourceMappingURL=index.js.map