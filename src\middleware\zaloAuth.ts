import { Request, Response, NextFunction } from 'express';
import crypto from 'crypto';
import { config } from '../config/config';
import { logger } from '../utils/logger';
import { AppError } from './errorHandler';

export interface ZaloWebhookRequest extends Request {
  rawBody?: Buffer;
}

// Middleware để lưu raw body cho việc verify signature
export const rawBodyParser = (req: ZaloWebhookRequest, res: Response, next: NextFunction): void => {
  let data = '';
  
  req.setEncoding('utf8');
  req.on('data', (chunk) => {
    data += chunk;
  });
  
  req.on('end', () => {
    req.rawBody = Buffer.from(data, 'utf8');
    req.body = data ? JSON.parse(data) : {};
    next();
  });
};

// Verify Zalo webhook signature
export const verifyZaloSignature = (req: ZaloWebhookRequest, res: Response, next: NextFunction): void => {
  try {
    const signature = req.headers['x-zalo-signature'] as string;
    const timestamp = req.headers['x-zalo-timestamp'] as string;
    
    // Nếu không có webhook secret, bỏ qua verification (development mode)
    if (!config.zalo.webhookSecret) {
      logger.warn('Webhook secret not configured - skipping signature verification');
      return next();
    }

    if (!signature || !timestamp) {
      throw new AppError('Missing Zalo signature or timestamp headers', 401);
    }

    // Verify timestamp (request should not be older than 5 minutes)
    const requestTime = parseInt(timestamp);
    const currentTime = Math.floor(Date.now() / 1000);
    const timeDiff = Math.abs(currentTime - requestTime);
    
    if (timeDiff > 300) { // 5 minutes
      throw new AppError('Request timestamp too old', 401);
    }

    // Create expected signature
    const rawBody = req.rawBody?.toString('utf8') || '';
    const signatureData = `${timestamp}${rawBody}`;
    const expectedSignature = crypto
      .createHmac('sha256', config.zalo.webhookSecret)
      .update(signatureData)
      .digest('hex');

    // Compare signatures
    if (!crypto.timingSafeEqual(Buffer.from(signature), Buffer.from(expectedSignature))) {
      throw new AppError('Invalid webhook signature', 401);
    }

    logger.info('Zalo webhook signature verified successfully');
    next();
  } catch (error) {
    logger.error('Zalo webhook signature verification failed:', error);
    next(error);
  }
};

// Validate Zalo webhook payload structure
export const validateZaloPayload = (req: Request, res: Response, next: NextFunction): void => {
  try {
    const { body } = req;

    if (!body) {
      throw new AppError('Empty request body', 400);
    }

    // Basic validation for Zalo webhook structure
    if (!body.app_id) {
      throw new AppError('Missing app_id in webhook payload', 400);
    }

    if (!body.timestamp) {
      throw new AppError('Missing timestamp in webhook payload', 400);
    }

    if (!body.event_name && !body.message) {
      throw new AppError('Missing event_name or message in webhook payload', 400);
    }

    logger.info('Zalo webhook payload validation passed', {
      app_id: body.app_id,
      event_name: body.event_name,
      user_id: body.user_id_by_app
    });

    next();
  } catch (error) {
    logger.error('Zalo webhook payload validation failed:', error);
    next(error);
  }
};
