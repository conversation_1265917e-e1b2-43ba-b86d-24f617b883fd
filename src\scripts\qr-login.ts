import { logger } from '../utils/logger';

// Declare require for Node.js
declare const require: any;

// Import zca-js
const { Zalo } = require('zca-js');

/**
 * Script để đăng nhập bằng QR code và lấy cookie
 * Chạy: yarn ts-node src/scripts/qr-login.ts
 */
async function loginWithQR(): Promise<void> {
  try {
    logger.info('🔐 Starting QR Code login process...');

    const zalo = new Zalo({
      selfListen: false,
    });

    // Đăng nhập bằng QR code
    logger.info('📱 Please scan the QR code that will appear...');
    const api = await zalo.loginQR();

    // Lấy cookie sau khi đăng nhập thành công
    const cookie = await api.getCookie();

    logger.info('✅ Login successful!');
    logger.info('🍪 Your cookie (save this to .env file):');
    console.log('\n' + '='.repeat(80));
    console.log('ZALO_COOKIE=' + cookie);
    console.log('='.repeat(80) + '\n');

    // Lấy thông tin tài khoản
    const accountInfo = await api.fetchAccountInfo();
    logger.info(`👤 Logged in as: ${accountInfo.name} (${accountInfo.id})`);

    logger.info('💡 Copy the cookie above and paste it into your .env file');
    logger.info('🚀 Then you can start your bot with: yarn dev');

  } catch (error) {
    logger.error('❌ QR Login failed:', error);
    process.exit(1);
  }
}

// Chạy script
loginWithQR().catch(error => {
  logger.error('💥 Unhandled error:', error);
  process.exit(1);
});
