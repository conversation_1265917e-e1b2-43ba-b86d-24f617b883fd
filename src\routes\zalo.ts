import { Router } from 'express';
import { <PERSON>alo<PERSON>ontroller } from '../controllers/zaloController';
import { verifyZaloSignature, validateZaloPayload, rawBodyParser } from '../middleware/zaloAuth';
import { logger } from '../utils/logger';

const router = Router();

// Test endpoint
router.get('/test', ZaloController.testEndpoint);

// Webhook endpoint để nhận tin nhắn từ Zalo
// Sử dụng raw body parser để verify signature
router.post('/webhook', 
  rawBodyParser,
  verifyZaloSignature,
  validateZaloPayload,
  ZaloController.handleWebhook
);

// Webhook verification endpoint (GET) - Zalo sẽ gọi để verify webhook URL
router.get('/webhook', (req, res) => {
  const challenge = req.query['hub.challenge'];
  const verifyToken = req.query['hub.verify_token'];
  
  logger.info('Webhook verification request:', {
    challenge,
    verifyToken,
    query: req.query
  });

  // Verify token (nếu có cấu hình)
  const expectedToken = process.env.ZALO_VERIFY_TOKEN;
  if (expectedToken && verifyToken !== expectedToken) {
    logger.error('Invalid verify token');
    return res.status(403).json({ error: 'Invalid verify token' });
  }

  // Trả về challenge để verify webhook
  if (challenge) {
    logger.info('Webhook verification successful');
    return res.status(200).send(challenge);
  }

  res.status(400).json({ error: 'Missing challenge parameter' });
});

// Health check endpoint cho Zalo API
router.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    service: 'Zalo API',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

export { router as zaloRoutes };
