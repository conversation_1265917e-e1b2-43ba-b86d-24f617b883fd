"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExpressApp = void 0;
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const body_parser_1 = __importDefault(require("body-parser"));
const config_1 = require("./config/config");
const logger_1 = require("./utils/logger");
const errorHandler_1 = require("./middleware/errorHandler");
const zalo_1 = require("./routes/zalo");
class ExpressApp {
    app;
    constructor() {
        this.app = (0, express_1.default)();
        this.setupMiddleware();
        this.setupRoutes();
        this.setupErrorHandling();
    }
    setupMiddleware() {
        this.app.use((0, helmet_1.default)({
            contentSecurityPolicy: false,
            crossOriginEmbedderPolicy: false
        }));
        this.app.use((0, cors_1.default)({
            origin: process.env.ALLOWED_ORIGINS?.split(',') || '*',
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
            allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
            credentials: true
        }));
        this.app.use((0, morgan_1.default)('combined', {
            stream: {
                write: (message) => {
                    logger_1.logger.info(message.trim());
                }
            }
        }));
        this.app.use(body_parser_1.default.json({ limit: '10mb' }));
        this.app.use(body_parser_1.default.urlencoded({ extended: true, limit: '10mb' }));
        this.app.use((req, res, next) => {
            logger_1.logger.info(`${req.method} ${req.path} - ${req.ip}`);
            next();
        });
    }
    setupRoutes() {
        this.app.get('/health', (req, res) => {
            res.status(200).json({
                status: 'OK',
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                environment: config_1.config.app.environment,
                version: config_1.config.app.version
            });
        });
        this.app.use(config_1.config.app.apiPrefix, zalo_1.zaloRoutes);
        this.app.get('/', (req, res) => {
            res.status(200).json({
                message: `${config_1.config.app.name} API is running`,
                version: config_1.config.app.version,
                environment: config_1.config.app.environment,
                endpoints: {
                    health: '/health',
                    api: config_1.config.app.apiPrefix
                }
            });
        });
        this.app.use('*', (req, res) => {
            res.status(404).json({
                error: 'Not Found',
                message: `Route ${req.originalUrl} not found`,
                timestamp: new Date().toISOString()
            });
        });
    }
    setupErrorHandling() {
        this.app.use(errorHandler_1.errorHandler);
    }
    getApp() {
        return this.app;
    }
    start() {
        const port = config_1.config.app.port;
        this.app.listen(port, () => {
            logger_1.logger.info(`🚀 Express server started on port ${port}`);
            logger_1.logger.info(`📍 Environment: ${config_1.config.app.environment}`);
            logger_1.logger.info(`🔗 API Base URL: http://localhost:${port}${config_1.config.app.apiPrefix}`);
            logger_1.logger.info(`💚 Health Check: http://localhost:${port}/health`);
        });
    }
}
exports.ExpressApp = ExpressApp;
//# sourceMappingURL=app.js.map