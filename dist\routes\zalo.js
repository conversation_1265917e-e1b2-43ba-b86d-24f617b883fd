"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.zaloRoutes = void 0;
const express_1 = require("express");
const zaloController_1 = require("../controllers/zaloController");
const zaloAuth_1 = require("../middleware/zaloAuth");
const logger_1 = require("../utils/logger");
const router = (0, express_1.Router)();
exports.zaloRoutes = router;
router.get('/test', zaloController_1.ZaloController.testEndpoint);
router.post('/webhook', zaloAuth_1.rawBodyParser, zaloAuth_1.verifyZaloSignature, zaloAuth_1.validateZaloPayload, zaloController_1.ZaloController.handleWebhook);
router.get('/webhook', (req, res) => {
    const challenge = req.query['hub.challenge'];
    const verifyToken = req.query['hub.verify_token'];
    logger_1.logger.info('Webhook verification request:', {
        challenge,
        verifyToken,
        query: req.query
    });
    const expectedToken = process.env.ZALO_VERIFY_TOKEN;
    if (expectedToken && verifyToken !== expectedToken) {
        logger_1.logger.error('Invalid verify token');
        return res.status(403).json({ error: 'Invalid verify token' });
    }
    if (challenge) {
        logger_1.logger.info('Webhook verification successful');
        return res.status(200).send(challenge);
    }
    res.status(400).json({ error: 'Missing challenge parameter' });
});
router.get('/health', (req, res) => {
    res.status(200).json({
        status: 'OK',
        service: 'Zalo API',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});
//# sourceMappingURL=zalo.js.map